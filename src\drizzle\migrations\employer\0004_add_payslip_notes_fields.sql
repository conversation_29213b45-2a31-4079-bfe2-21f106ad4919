-- Add show_on_payslip and is_repeating fields to payslip_notes table
-- This migration safely handles the case where columns might already exist

-- Strategy: Always recreate the table with the correct schema
-- This works for both new databases and existing ones with or without the new columns

-- Step 1: Create backup of existing data
CREATE TABLE payslip_notes_backup AS SELECT * FROM payslip_notes;

-- Step 2: Drop the existing table
DROP TABLE payslip_notes;

-- Step 3: Create the table with the new schema
CREATE TABLE payslip_notes (
    id TEXT PRIMARY KEY NOT NULL,
    payslip_id TEXT NOT NULL,
    content TEXT NOT NULL,
    show_on_payslip INTEGER DEFAULT 1 NOT NULL,
    is_repeating INTEGER DEFAULT 0 NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s','now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s','now')) NOT NULL,
    FOREIGN KEY (payslip_id) REFERENCES payslips(id) ON UPDATE no action ON DELETE no action
);

-- Step 4: Restore data
-- For simplicity, we'll always use default values for the new columns
-- This ensures the migration works regardless of the existing schema
INSERT INTO payslip_notes (id, payslip_id, content, show_on_payslip, is_repeating, created_at, updated_at)
SELECT
    id,
    payslip_id,
    content,
    1 as show_on_payslip,  -- Default to true (show on payslip)
    0 as is_repeating,     -- Default to false (not repeating)
    created_at,
    updated_at
FROM payslip_notes_backup;

-- Step 5: Create the index
CREATE INDEX idx_payslip_notes_payslip ON payslip_notes (payslip_id);

-- Step 6: Clean up
DROP TABLE payslip_notes_backup;
