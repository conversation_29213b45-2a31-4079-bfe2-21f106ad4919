import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";
import { useNavigationStore } from "@/store/navigation-store";
import { useQueryClient } from "@tanstack/react-query";
// Types
export interface OpenEmployer {
  id: string;
  name: string;
  dbPath: string;
  dbInstance: unknown; // Replace with Drizzle ORM instance type when available
  isLoading: boolean;
  hasUnsavedChanges: boolean;
}

interface EmployerDBContextType {
  openEmployers: OpenEmployer[];
  activeEmployerId: string | null;
  openEmployer: (employer: {
    id: string;
    name: string;
    dbPath: string;
  }) => Promise<void>;
  closeEmployer: (employerId: string) => Promise<void>;
  switchEmployer: (employerId: string) => void;
  setUnsavedChanges: (employerId: string, hasUnsaved: boolean) => void;
  openEmployerError: string | null;
  clearOpenEmployerError: () => void;
}

const EmployerDBContext = createContext<EmployerDBContextType | undefined>(
  undefined,
);

export const EmployerDBProvider = ({ children }: { children: ReactNode }) => {
  const [openEmployers, setOpenEmployers] = useState<OpenEmployer[]>([]);
  // Remove local activeEmployerId; always use Zustand
  const activeEmployerId = useNavigationStore((s) => s.activeEmployerId);
  const [openEmployerError, setOpenEmployerError] = useState<string | null>(
    null,
  );
  const queryClient = useQueryClient();

  // Open employer logic
  const openEmployer = useCallback(
    async (employer: { id: string; name: string; dbPath: string }) => {
      console.log("[openEmployer] called with:", employer);
      let isNew = false;
      let alreadyOpen = false;
      setOpenEmployers((prev) => {
        // If already open, just focus
        if (prev.some((e) => e.id === employer.id)) {
          alreadyOpen = true;
          return prev;
        }
        isNew = true;
        // Add new open employer (loading state true initially)
        return [
          ...prev,
          {
            ...employer,
            dbInstance: null,
            isLoading: true,
            hasUnsavedChanges: false,
          },
        ];
      });
      if (alreadyOpen) {
        // Defer navigation update to after render
        setTimeout(() => {
          useNavigationStore.getState().setActiveEmployer(employer.id);
        }, 0);
        return;
      }

      // --- NEW: Use service layer to open employer DB via IPC ---
      try {
        console.log(
          "[openEmployer] calling openEmployerDb with dbPath:",
          employer.dbPath,
        );
        const { openEmployerDb } = await import("@/services/employerDbService");
        const result = await openEmployerDb(employer.dbPath);
        console.log("[openEmployer] openEmployerDb result:", result);
        if (result.success) {
          setOpenEmployers((prev) =>
            prev.map((e) =>
              e.id === employer.id
                ? {
                    ...e,
                    dbInstance: null, // Placeholder; DB instance is only in main process
                    isLoading: false,
                  }
                : e,
            ),
          );

          // Now that the employer is open and loading is false, update Zustand navigation state
          try {
            const { setActiveEmployer, setEmployerSection } =
              useNavigationStore.getState();
            setActiveEmployer(employer.id);
            if (isNew) {
              setEmployerSection(employer.id, "payroll");
            }
            console.log(
              "[openEmployer] Set navigation state for employer",
              employer.id,
            );
          } catch (err) {
            console.log(
              "Failed to update navigation state after opening employer:",
              err,
            );
          }
        } else {
          setOpenEmployers((prev) => prev.filter((e) => e.id !== employer.id));
          setOpenEmployerError(
            result.error || "Failed to open employer database.",
          );
          console.log("Failed to open employer DB:", result.error);
        }
      } catch (error) {
        setOpenEmployers((prev) => prev.filter((e) => e.id !== employer.id));
        setOpenEmployerError(
          error instanceof Error ? error.message : String(error),
        );
        console.log("Failed to open employer DB (service error):", error);
      }
    },
    [],
  );

  // Close employer logic
  const closeEmployer = useCallback(
    async (employerId: string) => {
      // Find the employer to get the dbPath
      const employer = openEmployers.find((e) => e.id === employerId);
      if (employer) {
        try {
          // Invalidate all queries related to this employer's database
          await queryClient.invalidateQueries({
            predicate: (query) => {
              const queryKey = query.queryKey;
              // Check if query key contains the dbPath or employerId
              return (
                queryKey.includes(employer.dbPath) ||
                queryKey.includes(employerId)
              );
            },
          });

          // Remove all queries from cache for this employer
          queryClient.removeQueries({
            predicate: (query) => {
              const queryKey = query.queryKey;
              return (
                queryKey.includes(employer.dbPath) ||
                queryKey.includes(employerId)
              );
            },
          });

          // Close the database connection in the main process
          const { closeEmployerDb } = await import(
            "@/services/employerDbService"
          );
          await closeEmployerDb(employer.dbPath);
        } catch (error) {
          console.warn(
            `Failed to close employer database ${employer.dbPath}:`,
            error,
          );
          // Continue with UI cleanup even if database close fails
        }
      }

      setOpenEmployers((prev) => prev.filter((e) => e.id !== employerId));
      // If closing the active employer, update Zustand
      const zustandActiveEmployerId =
        useNavigationStore.getState().activeEmployerId;
      if (zustandActiveEmployerId === employerId) {
        useNavigationStore.getState().setActiveEmployer(null);
      }
    },
    [openEmployers, queryClient],
  );

  // Switch active employer
  // Switch active employer (Zustand is the source of truth)
  const switchEmployer = useCallback(
    (employerId: string) => {
      if (openEmployers.some((e) => e.id === employerId)) {
        useNavigationStore.getState().setActiveEmployer(employerId);
      }
    },
    [openEmployers],
  );

  // Set unsaved changes
  const setUnsavedChanges = useCallback(
    (employerId: string, hasUnsaved: boolean) => {
      setOpenEmployers((prev) =>
        prev.map((e) =>
          e.id === employerId ? { ...e, hasUnsavedChanges: hasUnsaved } : e,
        ),
      );
    },
    [],
  );

  const clearOpenEmployerError = useCallback(
    () => setOpenEmployerError(null),
    [],
  );

  const value: EmployerDBContextType = {
    openEmployers,
    activeEmployerId, // Now always from Zustand
    openEmployer,
    closeEmployer,
    switchEmployer,
    setUnsavedChanges,
    openEmployerError,
    clearOpenEmployerError,
  };

  return (
    <EmployerDBContext.Provider value={value}>
      {children}
    </EmployerDBContext.Provider>
  );
};

export function useEmployerDBContext() {
  const ctx = useContext(EmployerDBContext);
  if (!ctx)
    throw new Error(
      "useEmployerDBContext must be used within EmployerDBProvider",
    );
  return ctx;
}
